// Chat participant interface
export interface ChatParticipant {
  id: string;
  name: string;
  avatar: string;
}

// Chat room/group interface
export interface ChatRoom {
  id: string;
  name: string;
  lastMessage: string;
  timestamp: Date;
  isGroup: boolean;
  avatar: string;
  participants: ChatParticipant[];
}

// Chat message interface
export interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  content: string;
  timestamp: Date;
}

// Chat state interface for managing active room and messages
export interface ChatState {
  activeRoomId: string | null;
  rooms: ChatRoom[];
  messages: Record<string, ChatMessage[]>;
  isLoading: boolean;
  error: string | null;
  courseGroupsLoaded: boolean;
}

// API Request interfaces
export interface ChatGroupCreateRequest {
  name: string;
  description: string;
  type: string;
  course_id: string;
  is_private: boolean;
  initial_members: string[];
}

export interface ChatGroupAddMemberRequest {
  student_id?: string;
  teacher_id?: string;
  is_admin: boolean;
}

export interface ChatGroupInvitationRequest {
  invitee_id: string;
  invitee_type: string;
  message: string;
}

// API Response interfaces - Updated to match actual API response
export interface ChatGroupResponse {
  id: string;
  name: string;
  description: string;
  type: string;
  course_id: string;
  creator_id: string;
  creator_type: string;
  is_private: boolean;
  created_at: string;
  last_message_at: string | null;
  member_count: number;
  members: ChatGroupMember[];
}

export interface ChatGroupMember {
  id: string;
  chat_group_id: string;
  joined_at: string;
  is_admin: boolean;
  user_id: string;
  user_type: string;
  name: string;
  email: string;
  phone: string;
  profile_image_path: string;
}

// Interface for API course response
export interface APICourse {
  id: string;
  name: string;
  course_code?: string;
  description?: string;
  instructor?: {
    id: string;
    name: string;
    avatar?: string;
  };
  teacher_id?: string;
  teacher_name?: string;
}

// WebSocket message interface to handle different message formats from server
export interface WebSocketMessage {
  id?: string;
  _id?: string; // Alternative ID field from some APIs
  content?: string;
  timestamp?: string;
  created_at?: string; // Alternative timestamp field
  sender?: {
    id?: string;
    name?: string;
    avatar?: string;
  };
  sender_id?: string; // Alternative sender field format
  sender_name?: string;
  sender_avatar?: string;
  type?: string; // Message type (text, system, etc.)
  attachments?: any[];
}


